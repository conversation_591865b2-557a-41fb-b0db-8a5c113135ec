<script setup lang="tsx">
import { computed, onBeforeUnmount, onMounted, shallowRef, ref } from 'vue';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import type {
  AccountInfo,
  TableOrderInfo,
  ColumnDefinition,
  TickPoolDetail,
  CancelOrderBody,
  CancelOrderArray,
} from '@/types';
import { Utils } from '@/script';
import { RecordService } from '@/api';
import { useStockSelectionStore } from '@/stores';
import { ElMessage } from 'element-plus';

let interval = 0;

// 响应式数据
const orders = shallowRef<TableOrderInfo[]>([]);
const { setSelectedStock } = useStockSelectionStore();

// 选择状态管理
const selectedOrders = ref<Record<string, Set<1 | 2 | 3>>>({}); // 选中的委托：{股票代码: Set<委托序号>}
const clickedRowInstrument = ref<string>(''); // 当前点击的行的股票代码

// 处理行选择
const handleRowSelect = (row: TableOrderInfo, selected: boolean) => {
  if (selected) {
    // 选中行时，同时选中所有委托
    selectedOrders.value[row.instrument] = new Set([1, 2, 3]);
  } else {
    // 取消选中行时，清除所有委托选择
    delete selectedOrders.value[row.instrument];
  }
};

// 处理全选
const handleRowSelectAll = (selected: boolean) => {
  if (selected) {
    // 全选时，选中所有行的所有委托
    orders.value.forEach(order => {
      selectedOrders.value[order.instrument] = new Set([1, 2, 3]);
    });
  } else {
    // 取消全选时，清除所有委托选择
    selectedOrders.value = {};
  }
};

// 处理委托选择
const handleOrderSelect = (instrument: string, orderNo: 1 | 2 | 3, selected: boolean) => {
  if (!selectedOrders.value[instrument]) {
    selectedOrders.value[instrument] = new Set();
  }

  if (selected) {
    selectedOrders.value[instrument].add(orderNo);
  } else {
    selectedOrders.value[instrument].delete(orderNo);
    // 如果没有选中的委托了，清除该股票的委托选择
    if (selectedOrders.value[instrument].size === 0) {
      delete selectedOrders.value[instrument];
    }
  }
};

// 检查委托是否被选中
const isOrderSelected = (instrument: string, orderNo: 1 | 2 | 3) => {
  return selectedOrders.value[instrument]?.has(orderNo) || false;
};

// 撤单功能
const cancelOrders = async (cancelData: CancelOrderBody) => {
  try {
    const { errorCode, errorMsg } = await RecordService.cancelOrder(cancelData);
    if (errorCode === 0) {
      ElMessage.success('撤单成功');
      // 清除选择状态
      selectedOrders.value = {};
      // 刷新数据
      fetchOrders();
    } else {
      ElMessage.error(errorMsg || '撤单失败');
    }
  } catch {
    ElMessage.error('撤单失败');
  }
};

// 全撤功能（F1键或点击全撤按钮）
const cancelAllOrders = (instrument: string) => {
  const cancelData: CancelOrderBody = {
    [instrument]: [1, 2, 3],
  };
  cancelOrders(cancelData);
};

// 撤勾选功能
const cancelSelectedOrders = () => {
  if (Object.keys(selectedOrders.value).length === 0) {
    ElMessage.warning('请先选择要撤销的委托');
    return;
  }

  const cancelData: CancelOrderBody = {};
  Object.entries(selectedOrders.value).forEach(([instrument, orderNos]) => {
    if (orderNos.size > 0) {
      cancelData[instrument] = Array.from(orderNos).sort() as CancelOrderArray;
    }
  });

  cancelOrders(cancelData);
};

// 键盘事件处理
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'F1') {
    event.preventDefault();
    // 如果有点击的行，对该行执行全撤
    if (clickedRowInstrument.value) {
      const cancelData: CancelOrderBody = {
        [clickedRowInstrument.value]: [1, 2, 3],
      };
      cancelOrders(cancelData);
    }
  }
};

// 处理行点击事件
const handleRowClick = (row: TableOrderInfo) => {
  // 记录当前点击的行
  clickedRowInstrument.value = row.instrument;

  const stockInfo: TickPoolDetail = {
    id: 0,
    instrument: row.instrument,
    instrumentName: row.instrumentName,
    status: 1,
    risePercent: 0,
    poolName: '',
    poolId: 0,
    positionRate: 0,
  };
  setSelectedStock(stockInfo);
};
const account = shallowRef<AccountInfo | null>(null);

const accountName = computed(() => {
  if (account.value) {
    return `${account.value.accountId} ${account.value.accountName}`;
  } else {
    return '--';
  }
});

// 账号详情字段配置
const accountFields = computed(() => {
  return [
    {
      label: '可用资金比例',
      value: !account.value
        ? '--'
        : Utils.formatNumber(account.value.available / account.value.balance, { percent: true }),
      color: '',
    },
    {
      label: '仓位比例',
      value: !account.value
        ? '--'
        : Utils.formatNumber(account.value.marketValue / account.value.balance, { percent: true }),
      color: '',
    },
    {
      label: '可用',
      value: !account.value
        ? '--'
        : Utils.formatNumber(account.value.available, { separator: true }),
      color: '',
    },
    {
      label: '总资产',
      value: !account.value ? '--' : Utils.formatNumber(account.value.balance, { separator: true }),
      color: '',
    },
    {
      label: '总市值',
      value: !account.value
        ? '--'
        : Utils.formatNumber(account.value.marketValue, { separator: true }),
      color: '',
    },
    {
      label: '盈亏',
      value: !account.value
        ? '--'
        : Utils.formatNumber(account.value.dayProfit, { separator: true }),
      color: !account.value ? '' : account.value.dayProfit >= 0 ? 'c-[--g-red]' : 'c-[--g-green]',
    },
  ];
});

// 委托列表表格列定义
const orderColumns: ColumnDefinition<TableOrderInfo> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '证券代码',
    width: 200,
  },
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '证券名称',
    width: 200,
  },
  {
    key: 'positionEffect',
    dataKey: 'positionEffect',
    title: '仓位',
    width: 200,
    cellRenderer: ({ cellData }) => <span>{Utils.formatNumber(cellData, { default: '--' })}%</span>,
  },
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '全撤',
    width: 200,
    cellRenderer: ({ rowData }) => (
      <el-button color="var(--g-red)" onClick={() => cancelAllOrders(rowData.instrument)}>
        全撤/F1
      </el-button>
    ),
  },
  {
    key: 'orderSummaryList',
    dataKey: 'orderSummaryList',
    title: '委托1',
    width: 200,
    cellRenderer: ({ cellData, rowData }) => (
      <div class="flex aic gap-10">
        <el-checkbox
          modelValue={isOrderSelected(rowData.instrument, 1)}
          onChange={(checked: boolean) => handleOrderSelect(rowData.instrument, 1, checked)}
        />
        <div
          title={`委托数量：${Utils.formatNumber(cellData[0].totalOrderVolume, { fix: 0, separator: true })}，已成交数量：${Utils.formatNumber(cellData[0].totalTradeVolume, { fix: 0, separator: true })}`}
        >
          <div class="flex aic gap-4">
            <div>委:</div>
            <div class="flex-1 min-w-1">
              {Utils.formatNumber(cellData[0].totalOrderVolume, { fix: 0, separator: true })}
            </div>
          </div>
          <div class="flex aic gap-4">
            <div>成:</div>
            <div class="flex-1 min-w-1">
              {Utils.formatNumber(cellData[0].totalTradeVolume, { fix: 0, separator: true })}
            </div>
          </div>
        </div>
      </div>
    ),
  },
  {
    key: 'orderSummaryList',
    dataKey: 'orderSummaryList',
    title: '委托2',
    width: 200,
    cellRenderer: ({ cellData, rowData }) => (
      <div class="flex aic gap-10">
        <el-checkbox
          modelValue={isOrderSelected(rowData.instrument, 2)}
          onChange={(checked: boolean) => handleOrderSelect(rowData.instrument, 2, checked)}
        />
        <div
          title={`委托数量：${Utils.formatNumber(cellData[1].totalOrderVolume, { fix: 0, separator: true })}，已成交数量：${Utils.formatNumber(cellData[1].totalTradeVolume, { fix: 0, separator: true })}`}
        >
          <div class="flex aic gap-4">
            <div>委:</div>
            <div class="flex-1 min-w-1">
              {Utils.formatNumber(cellData[1].totalOrderVolume, { fix: 0, separator: true })}
            </div>
          </div>
          <div class="flex aic gap-4">
            <div>成:</div>
            <div class="flex-1 min-w-1">
              {Utils.formatNumber(cellData[1].totalTradeVolume, { fix: 0, separator: true })}
            </div>
          </div>
        </div>
      </div>
    ),
  },
  {
    key: 'orderSummaryList',
    dataKey: 'orderSummaryList',
    title: '委托3',
    width: 200,
    cellRenderer: ({ cellData, rowData }) => (
      <div class="flex aic gap-10">
        <el-checkbox
          modelValue={isOrderSelected(rowData.instrument, 3)}
          onChange={(checked: boolean) => handleOrderSelect(rowData.instrument, 3, checked)}
        />
        <div
          title={`委托数量：${Utils.formatNumber(cellData[2].totalOrderVolume, { fix: 0, separator: true })}，已成交数量：${Utils.formatNumber(cellData[2].totalTradeVolume, { fix: 0, separator: true })}`}
        >
          <div class="flex aic gap-4">
            <div>委:</div>
            <div class="flex-1 min-w-1">
              {Utils.formatNumber(cellData[2].totalOrderVolume, { fix: 0, separator: true })}
            </div>
          </div>
          <div class="flex aic gap-4">
            <div>成:</div>
            <div class="flex-1 min-w-1">
              {Utils.formatNumber(cellData[2].totalTradeVolume, { fix: 0, separator: true })}
            </div>
          </div>
        </div>
      </div>
    ),
  },
];

// 获取委托列表
const fetchOrders = async () => {
  const { errorCode, data } = await RecordService.getOrders();
  if (errorCode === 0 && data) {
    orders.value = data;
  }
};

const getAccountInfo = async () => {
  const { errorCode, data } = await RecordService.getAccountInfo();
  if (errorCode === 0 && data) {
    account.value = data;
  }
};

const init = () => {
  fetchOrders();
  getAccountInfo();
};

// 组件挂载时获取数据
onMounted(() => {
  init();
  interval = window.setInterval(init, 5000);
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyDown);
});

onBeforeUnmount(() => {
  clearInterval(interval);
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeyDown);
});
</script>

<template>
  <div flex="~ col" h-full>
    <!-- 委托标题 -->
    <div h-32 flex aic px-16 border-b="1 solid [--el-border-color]">
      <span text-14 font-bold>委托</span>
    </div>

    <!-- 账号详情区域 -->
    <div p-16 border-b="1 solid [--el-border-color]">
      <div flex aic gap-8 mb-12>
        <div>账号</div>
        <div c-white>
          {{ accountName }}
        </div>
      </div>

      <!-- 账号详情字段 -->
      <div flex gap-24>
        <div v-for="field in accountFields" :key="field.label" flex="~ col" gap-4>
          <span>{{ field.label }}</span>
          <span :class="field.color || 'text-white'">{{ field.value }}</span>
        </div>
      </div>
    </div>

    <div px-16 h-32 flex aic flex-justify-end>
      <el-button @click="cancelSelectedOrders">撤勾选</el-button>
    </div>
    <!-- 委托列表 -->
    <div flex-1 min-h-1>
      <VirtualizedTable
        select
        identity="instrument"
        :columns="orderColumns"
        :data="orders"
        @row-click="handleRowClick"
        @row-select="handleRowSelect"
        @row-select-all="handleRowSelectAll"
      />
    </div>
  </div>
</template>

<style scoped></style>
