<script setup lang="ts">
import { computed, shallowRef, watch } from 'vue';
import { ElMessage, ElTabs, ElTabPane, ElButton, ElCheckbox } from 'element-plus';
import type { TablePositionInfo, SellSetting } from '@/types';
import { RecordService } from '@/api';
import { BuyLevelsEnum } from '@/enum';
import LowerVolumeConfig from './SellStrategySetting/LowerVolumeConfig.vue';
import ProfitLossConfig from './SellStrategySetting/ProfitLossConfig.vue';
import TimedConfig from './SellStrategySetting/TimedConfig.vue';

const props = defineProps<{
  selectedPosition: TablePositionInfo | null;
}>();

const emit = defineEmits<{
  strategySaved: [];
}>();

// 当前激活的tab
const activeTab = shallowRef('lowerVolume');

// 表单数据
const formData = shallowRef<SellSetting>({
  openLower: false,
  openProfitLoss: false,
  openTimed: false,
  lowerVolume: {
    priority: {
      startTime: '13:49:43',
      endTime: '13:49:43',
      positionPercent: 20,
      lineUpVolumeAmount: 20,
      lineUpVolumeAmount1: 300,
      time: 30,
      downRate: 30,
    },
    global: {
      startTime: '13:49:43',
      endTime: '13:49:43',
      positionPercent: 20,
      lineUpVolumeAmount: 20,
      lineUpVolumeAmount1: 300,
      time: 30,
      downRate: 30,
    },
  },
  profitLoss: {
    open: false,
    time: 1000,
    pricePercent: 30,
    takeProfit: [
      { amplitude: 20, positionPercent: 20, open: false },
      { amplitude: 8, positionPercent: 300, open: false },
      { amplitude: 30, positionPercent: 30, open: false },
      { amplitude: 0, positionPercent: 0, open: false },
      { amplitude: 0, positionPercent: 0, open: false },
    ],
    StopLoss: [
      { amplitude: 20, positionPercent: 20, open: false },
      { amplitude: 8, positionPercent: 300, open: false },
      { amplitude: 30, positionPercent: 30, open: false },
      { amplitude: 0, positionPercent: 0, open: false },
      { amplitude: 0, positionPercent: 0, open: false },
    ],
  },
  timed: {
    strategyDelayTime: 48,
    strategyVolume: 1000,
    cancelProtectedTime: 48,
    positionPercent: 48,
    priceFollowType: BuyLevelsEnum.买一,
  },
});

// 原始数据备份，用于取消时还原
const originalData = shallowRef<SellSetting | null>(null);

// 是否有选中的持仓
const hasSelectedPosition = computed(() => props.selectedPosition !== null);

// 当前持仓的策略ID
const currentStrategyId = computed(() => props.selectedPosition?.sellStrategy?.id || null);

// 监听选中持仓变化，更新表单数据
watch(
  () => props.selectedPosition,
  newPosition => {
    if (newPosition) {
      if (newPosition.sellStrategy) {
        // 如果有策略配置，使用策略配置
        formData.value = JSON.parse(JSON.stringify(newPosition.sellStrategy.setting));
        originalData.value = JSON.parse(JSON.stringify(newPosition.sellStrategy.setting));
      } else {
        // 如果没有策略配置，使用默认配置
        resetToDefault();
      }
    }
  },
  { immediate: true },
);

// 重置为默认配置
const resetToDefault = () => {
  formData.value = {
    openLower: false,
    openProfitLoss: false,
    openTimed: false,
    lowerVolume: {
      priority: {
        startTime: '13:49:43',
        endTime: '13:49:43',
        positionPercent: 20,
        lineUpVolumeAmount: 20,
        lineUpVolumeAmount1: 300,
        time: 30,
        downRate: 30,
      },
      global: {
        startTime: '13:49:43',
        endTime: '13:49:43',
        positionPercent: 20,
        lineUpVolumeAmount: 20,
        lineUpVolumeAmount1: 300,
        time: 30,
        downRate: 30,
      },
    },
    profitLoss: {
      open: false,
      time: 1000,
      pricePercent: 30,
      takeProfit: [
        { amplitude: 20, positionPercent: 20, open: false },
        { amplitude: 8, positionPercent: 300, open: false },
        { amplitude: 30, positionPercent: 30, open: false },
        { amplitude: 0, positionPercent: 0, open: false },
        { amplitude: 0, positionPercent: 0, open: false },
      ],
      StopLoss: [
        { amplitude: 20, positionPercent: 20, open: false },
        { amplitude: 8, positionPercent: 300, open: false },
        { amplitude: 30, positionPercent: 30, open: false },
        { amplitude: 0, positionPercent: 0, open: false },
        { amplitude: 0, positionPercent: 0, open: false },
      ],
    },
    timed: {
      strategyDelayTime: 48,
      strategyVolume: 1000,
      cancelProtectedTime: 48,
      positionPercent: 48,
      priceFollowType: BuyLevelsEnum.买一,
    },
  };
  originalData.value = JSON.parse(JSON.stringify(formData.value));
};

// 取消操作
const handleCancel = () => {
  if (originalData.value) {
    formData.value = JSON.parse(JSON.stringify(originalData.value));
  }
};

// 保存操作
const handleSave = async () => {
  if (!props.selectedPosition) {
    ElMessage.warning('请先选择持仓');
    return;
  }

  try {
    const saveData = {
      instrument: props.selectedPosition.instrument,
      instrumentName: props.selectedPosition.instrumentName,
      setting: formData.value,
    };

    // 如果持仓的setting不为null，则传id进行更新
    if (currentStrategyId.value) {
      await RecordService.saveSellStrategy({ ...saveData, id: currentStrategyId.value });
    } else {
      await RecordService.saveSellStrategy(saveData);
    }

    ElMessage.success('保存成功');
    originalData.value = JSON.parse(JSON.stringify(formData.value));
    emit('strategySaved');
  } catch (error) {
    ElMessage.error('保存失败');
  }
};
</script>

<template>
  <div flex="~ col" h-full>
    <div h-32 flex aic px-16 border-b="1 solid [--el-border-color]">
      <span text-14 font-bold>卖出策略</span>
    </div>

    <div v-if="!hasSelectedPosition" flex="~ 1" items-center justify-center>
      <span text-gray-400>请选择持仓</span>
    </div>

    <div v-else flex="~ col 1" min-h-0>
      <!-- 选中持仓信息 -->
      <div flex="~ none" p-16 border-b="1 solid [--el-border-color]">
        <div>
          <div font-medium>{{ selectedPosition?.instrumentName }}</div>
          <div text-gray-400>{{ selectedPosition?.instrument }}</div>
        </div>
      </div>

      <!-- 策略配置 -->
      <div flex="~ col 1" min-h-0 p-16>
        <ElTabs v-model="activeTab" class="flex-1">
          <ElTabPane label="低封单量" name="lowerVolume">
            <div flex="~ col" gap-4>
              <ElCheckbox v-model="formData.openLower">启用低封单量</ElCheckbox>
              <LowerVolumeConfig v-model="formData.lowerVolume" :disabled="!formData.openLower" />
            </div>
          </ElTabPane>

          <ElTabPane label="止盈止损" name="profitLoss">
            <div flex="~ col" gap-4>
              <ElCheckbox v-model="formData.openProfitLoss">启用止盈止损</ElCheckbox>
              <ProfitLossConfig
                v-model="formData.profitLoss"
                :disabled="!formData.openProfitLoss"
              />
            </div>
          </ElTabPane>

          <ElTabPane label="定时定量" name="timed">
            <div flex="~ col" gap-4>
              <ElCheckbox v-model="formData.openTimed">启用定时定量</ElCheckbox>
              <TimedConfig v-model="formData.timed" :disabled="!formData.openTimed" />
            </div>
          </ElTabPane>
        </ElTabs>

        <!-- 操作按钮 -->
        <div flex="~ none" justify-end gap-2 mt-4 pt-4 border-t="1 solid [--el-border-color]">
          <ElButton @click="handleCancel">取消</ElButton>
          <ElButton type="primary" @click="handleSave">保存</ElButton>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.el-tabs__content) {
  flex: 1;
  overflow-y: auto;
}
</style>
