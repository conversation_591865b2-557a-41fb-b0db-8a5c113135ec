<script setup lang="ts">
import { ElInputNumber, ElTimePicker } from 'element-plus';
import type { SellSetting } from '@/types';

const props = defineProps<{
  modelValue: SellSetting['lowerVolume'];
  disabled?: boolean;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: SellSetting['lowerVolume']];
}>();

// 更新数据
const updateValue = (key: keyof SellSetting['lowerVolume'], subKey: string, value: any) => {
  const newValue = { ...props.modelValue };
  (newValue[key] as any)[subKey] = value;
  emit('update:modelValue', newValue);
};

// 时间格式化
const timeFormat = 'HH:mm:ss';
</script>

<template>
  <div flex="~ col" gap-4 :class="{ 'opacity-50 pointer-events-none': disabled }">
    <!-- 优先配置 -->
    <div>
      <div text-sm font-medium mb-2>优先</div>
      <div grid="~ cols-2" gap-4>
        <div>
          <label text-xs text-gray-400>开始时间</label>
          <ElTimePicker
            :model-value="modelValue.priority.startTime"
            :format="timeFormat"
            :value-format="timeFormat"
            placeholder="选择时间"
            size="small"
            @update:model-value="(val) => updateValue('priority', 'startTime', val)"
          />
        </div>
        <div>
          <label text-xs text-gray-400>结束时间</label>
          <ElTimePicker
            :model-value="modelValue.priority.endTime"
            :format="timeFormat"
            :value-format="timeFormat"
            placeholder="选择时间"
            size="small"
            @update:model-value="(val) => updateValue('priority', 'endTime', val)"
          />
        </div>
        <div>
          <label text-xs text-gray-400>卖出仓位(%)</label>
          <ElInputNumber
            :model-value="modelValue.priority.positionPercent"
            :min="0"
            :max="100"
            size="small"
            @update:model-value="(val) => updateValue('priority', 'positionPercent', val)"
          />
        </div>
        <div>
          <label text-xs text-gray-400>封单金额少于(亿)</label>
          <ElInputNumber
            :model-value="modelValue.priority.lineUpVolumeAmount"
            :min="0"
            size="small"
            @update:model-value="(val) => updateValue('priority', 'lineUpVolumeAmount', val)"
          />
        </div>
        <div>
          <label text-xs text-gray-400>封单金额少于(亿)</label>
          <ElInputNumber
            :model-value="modelValue.priority.lineUpVolumeAmount1"
            :min="0"
            size="small"
            @update:model-value="(val) => updateValue('priority', 'lineUpVolumeAmount1', val)"
          />
        </div>
        <div>
          <label text-xs text-gray-400>时间(毫秒)</label>
          <ElInputNumber
            :model-value="modelValue.priority.time"
            :min="0"
            size="small"
            @update:model-value="(val) => updateValue('priority', 'time', val)"
          />
        </div>
        <div>
          <label text-xs text-gray-400>封单下降(%)</label>
          <ElInputNumber
            :model-value="modelValue.priority.downRate"
            :min="0"
            :max="100"
            size="small"
            @update:model-value="(val) => updateValue('priority', 'downRate', val)"
          />
        </div>
      </div>
    </div>

    <!-- 全局配置 -->
    <div>
      <div text-sm font-medium mb-2>全局</div>
      <div grid="~ cols-2" gap-4>
        <div>
          <label text-xs text-gray-400>开始时间</label>
          <ElTimePicker
            :model-value="modelValue.global.startTime"
            :format="timeFormat"
            :value-format="timeFormat"
            placeholder="选择时间"
            size="small"
            @update:model-value="(val) => updateValue('global', 'startTime', val)"
          />
        </div>
        <div>
          <label text-xs text-gray-400>结束时间</label>
          <ElTimePicker
            :model-value="modelValue.global.endTime"
            :format="timeFormat"
            :value-format="timeFormat"
            placeholder="选择时间"
            size="small"
            @update:model-value="(val) => updateValue('global', 'endTime', val)"
          />
        </div>
        <div>
          <label text-xs text-gray-400>卖出仓位(%)</label>
          <ElInputNumber
            :model-value="modelValue.global.positionPercent"
            :min="0"
            :max="100"
            size="small"
            @update:model-value="(val) => updateValue('global', 'positionPercent', val)"
          />
        </div>
        <div>
          <label text-xs text-gray-400>封单金额少于(亿)</label>
          <ElInputNumber
            :model-value="modelValue.global.lineUpVolumeAmount"
            :min="0"
            size="small"
            @update:model-value="(val) => updateValue('global', 'lineUpVolumeAmount', val)"
          />
        </div>
        <div>
          <label text-xs text-gray-400>封单金额少于(亿)</label>
          <ElInputNumber
            :model-value="modelValue.global.lineUpVolumeAmount1"
            :min="0"
            size="small"
            @update:model-value="(val) => updateValue('global', 'lineUpVolumeAmount1', val)"
          />
        </div>
        <div>
          <label text-xs text-gray-400>时间(毫秒)</label>
          <ElInputNumber
            :model-value="modelValue.global.time"
            :min="0"
            size="small"
            @update:model-value="(val) => updateValue('global', 'time', val)"
          />
        </div>
        <div>
          <label text-xs text-gray-400>封单下降(%)</label>
          <ElInputNumber
            :model-value="modelValue.global.downRate"
            :min="0"
            :max="100"
            size="small"
            @update:model-value="(val) => updateValue('global', 'downRate', val)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.el-input-number {
  width: 100%;
}
.el-time-picker {
  width: 100%;
}
</style>
