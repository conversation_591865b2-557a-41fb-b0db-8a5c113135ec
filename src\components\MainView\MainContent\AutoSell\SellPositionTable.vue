<script setup lang="tsx">
import { onBeforeUnmount, onMounted, shallowRef } from 'vue';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import type { TablePositionInfo, ColumnDefinition } from '@/types';
import { Utils } from '@/script';
import { RecordService } from '@/api';
import { TacticStatusEnum } from '@/enum';

const emit = defineEmits<{
  positionSelect: [position: TablePositionInfo];
}>();

let interval = 0;

// 响应式数据
const positions = shallowRef<TablePositionInfo[]>([]);
const selectedRowIndex = shallowRef(-1);

// 处理行点击事件
const handleRowClick = (row: TablePositionInfo, index: number) => {
  selectedRowIndex.value = index;
  emit('positionSelect', row);
};

// 持仓列表表格列定义
const positionColumns: ColumnDefinition<TablePositionInfo> = [
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '证券名称',
    width: 200,
  },
  {
    key: 'status',
    dataKey: 'status',
    title: '策略状态',
    width: 160,
    cellRenderer: ({ rowData }) => {
      const statusText = rowData.status === TacticStatusEnum.运行中 ? '运行中' : '已停止';
      const color = rowData.status === TacticStatusEnum.运行中 ? 'c-[var(--g-green)]' : 'c-gray';
      return <span class={color}>{statusText}</span>;
    },
  },
  {
    key: 'risePercent',
    dataKey: 'risePercent',
    title: '涨跌幅',
    width: 160,
    cellRenderer: ({ rowData }) => {
      // 计算涨跌幅 (浮动盈亏 / 市值) * 100
      const risePercent =
        rowData.marketValue > 0 ? (rowData.dayProfit / rowData.marketValue) * 100 : 0;
      const color =
        risePercent > 0 ? 'c-[var(--g-red)]' : risePercent < 0 ? 'c-[var(--g-green)]' : 'c-white';
      return (
        <span class={color}>
          {Utils.formatNumber(risePercent, { fix: 2, prefix: true, default: '--' })}%
        </span>
      );
    },
  },
  {
    key: 'todayPosition',
    dataKey: 'todayPosition',
    title: '持仓数量',
    width: 160,
    cellRenderer: ({ cellData }) => (
      <span>{Utils.formatNumber(cellData, { separator: true })}</span>
    ),
  },
  {
    key: 'yesterdayPosition',
    dataKey: 'yesterdayPosition',
    title: '可用数量',
    width: 160,
    cellRenderer: ({ cellData }) => (
      <span>{Utils.formatNumber(cellData, { separator: true })}</span>
    ),
  },
  {
    key: 'marketValue',
    dataKey: 'marketValue',
    title: '市值',
    width: 200,
    cellRenderer: ({ cellData }) => (
      <span>{Utils.formatNumber(cellData, { separator: true })}</span>
    ),
  },
  {
    key: 'positionEffect',
    dataKey: 'positionEffect',
    title: '仓位',
    width: 120,
    cellRenderer: ({ cellData }) => <span>{Utils.formatNumber(cellData, { default: '--' })}%</span>,
  },
  {
    key: 'dayProfit',
    dataKey: 'dayProfit',
    title: '浮动盈亏',
    width: 200,
    cellRenderer: ({ cellData }) => {
      const color =
        cellData > 0 ? 'c-[var(--g-red)]' : cellData < 0 ? 'c-[var(--g-green)]' : 'c-white';
      return (
        <span class={color}>
          {Utils.formatNumber(cellData, { fix: 2, prefix: true, separator: true })}
        </span>
      );
    },
  },
  {
    key: 'profitRatio',
    dataKey: 'profitRatio',
    title: '盈亏比',
    width: 140,
    cellRenderer: ({ rowData }) => {
      // 计算盈亏比 (浮动盈亏 / 市值) * 100
      const profitRatio =
        rowData.marketValue > 0 ? (rowData.dayProfit / rowData.marketValue) * 100 : 0;
      const color =
        profitRatio > 0 ? 'c-[var(--g-red)]' : profitRatio < 0 ? 'c-[var(--g-green)]' : 'c-white';
      return <span class={color}>{Utils.formatNumber(profitRatio, { fix: 2 })}%</span>;
    },
  },
];

// 获取持仓列表
const fetchPositions = async () => {
  const { errorCode, data } = await RecordService.getPositions();
  if (errorCode === 0 && data) {
    positions.value = data;
  }
};

// 刷新数据的方法，供父组件调用
const refreshData = () => {
  fetchPositions();
};

// 组件挂载时获取数据
onMounted(() => {
  fetchPositions();
  interval = window.setInterval(fetchPositions, 5000);
});

onBeforeUnmount(() => {
  clearInterval(interval);
});

// 暴露方法给父组件
defineExpose({
  refreshData,
});
</script>

<template>
  <div flex="~ col">
    <div h-32 flex aic px-16 border-b="1 solid [--el-border-color]">
      <span text-14 font-bold>持仓</span>
    </div>
    <div flex="~ 1" min-h-0>
      <VirtualizedTable
        w-full
        identity="instrument"
        :data="positions"
        :columns="positionColumns"
        :selected-index="selectedRowIndex"
        @row-click="handleRowClick"
      />
    </div>
  </div>
</template>

<style scoped></style>
