<script setup lang="tsx">
import type { ColumnDefinition, TickPoolDetail, RowAction, WsPoolDetail } from '@/types';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import { shallowRef, onMounted, ref } from 'vue';
import { Misc, Utils } from '@/script';
import { ElMessage, ElMessageBox } from 'element-plus';
import { PoolService } from '@/api';
import { TacticStatusEnum, isStopped } from '@/enum';
import { useStockSelectionStore } from '@/stores';
import { useWebSocketPush } from '@/composables/useWebSocketPush';

const columns: ColumnDefinition<TickPoolDetail> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '证券代码',
    width: 200,
  },
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '证券名称',
    width: 200,
  },
  {
    key: 'risePercent',
    dataKey: 'risePercent',
    title: '涨跌幅',
    sortable: true,
    width: 200,
    cellRenderer: ({ cellData }) => {
      return (
        <span class={cellData && cellData > 0 ? 'c-[var(--g-red)]' : 'c-[var(--g-green)]'}>
          {Utils.formatNumber(cellData, { percent: true, prefix: true })}
        </span>
      );
    },
  },
  {
    key: 'poolName',
    dataKey: 'poolName',
    title: '股票池',
    width: 200,
  },
  {
    key: 'positionRate',
    dataKey: 'positionRate',
    title: '仓位',
    width: 200,
    cellRenderer: ({ cellData }) => {
      return <span>{Utils.formatNumber(cellData, { default: '--' })}%</span>;
    },
  },
  {
    key: 'poolId',
    dataKey: 'poolId',
    title: '启停',
    width: 200,
    cellRenderer: ({ rowData }: { rowData: TickPoolDetail }) => {
      return (
        <el-switch
          modelValue={isStopped(rowData) ? TacticStatusEnum.新建 : TacticStatusEnum.运行中}
          active-value={1}
          inactive-value={0}
          before-change={() => beforeChange(rowData)}
        />
      );
    },
  },
  {
    key: 'status',
    dataKey: 'status',
    title: '状态',
    width: 200,
    cellRenderer: ({ cellData }) => {
      const text = TacticStatusEnum[cellData];
      return <span>{text}</span>;
    },
  },
];

const rowActions: RowAction<TickPoolDetail>[] = [
  {
    label: '删除',
    onClick: row => {
      ElMessageBox.confirm('确认删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const { errorCode, errorMsg } = await PoolService.deletePoolDetail(row.id);
        if (errorCode === 0) {
          ElMessage.success('删除成功');
          // 不再手动删除，等待WebSocket推送更新
        } else {
          ElMessage.error(errorMsg || '删除失败');
        }
      });
    },
    color: 'var(--g-red)',
  },
];

const monitorInfos = ref<TickPoolDetail[]>([]);
const { setSelectedStock } = useStockSelectionStore();
const { onPoolDetailUpdate } = useWebSocketPush();

onMounted(() => {
  getData();

  // 监听股票池详情推送更新
  onPoolDetailUpdate((detail: WsPoolDetail) => {
    if (detail.deleted === 1) {
      // 删除股票池详情
      monitorInfos.value = monitorInfos.value.filter(x => x.id !== detail.id);
    } else {
      // 新增或更新股票池详情
      const existingIndex = monitorInfos.value.findIndex(x => x.id === detail.id);
      if (existingIndex >= 0) {
        // 更新现有详情，保留涨跌幅数据
        monitorInfos.value[existingIndex] = {
          ...detail,
          risePercent: monitorInfos.value[existingIndex].risePercent,
        };
      } else {
        // 新增详情
        monitorInfos.value.push(detail as TickPoolDetail);
      }
    }
  });
});

/** 点击行时，设置选中股票 */
const handleRowClick = (row: TickPoolDetail) => {
  setSelectedStock(row);
};

const getData = async () => {
  const { errorCode, data } = await PoolService.getAllPoolDetails();
  if (errorCode === 0 && data) {
    monitorInfos.value = data;
  }
};

const handleConfig = () => {};
const handleStart = () => {};
const handleStop = () => {};
const handleRefresh = () => {};
/** 进行启停操作，成功返回true,否则返回false */
const beforeChange = async (rowData: TickPoolDetail) => {
  const { errorCode, errorMsg } = await PoolService.updatePoolDetailStatus(
    rowData.id,
    isStopped(rowData),
  );
  if (errorCode === 0) {
    Misc.putRow(
      {
        ...rowData,
        status: isStopped(rowData) ? TacticStatusEnum.运行中 : TacticStatusEnum.新建,
      },
      monitorInfos,
    );
    return true;
  } else {
    ElMessage.error(errorMsg || '操作失败');
    return false;
  }
};
</script>

<template>
  <div flex="~ col">
    <div flex aic jcsb px-16 h-32>
      <div text-14 font-bold>监控池</div>
      <div>
        <el-button @click="handleConfig">
          <i mr-4 fs-14 i-mdi-cog-outline />
          设置
        </el-button>
        <el-button color="var(--g-bg-green)" @click="handleStart">
          <i mr-4 fs-14 i-mdi-motion-play-outline />
          一键启动
        </el-button>
        <el-button color="var(--g-red)" @click="handleStop">
          <i mr-4 fs-14 i-mdi-motion-pause-outline />
          一键停止
        </el-button>
        <el-button color="var(--g-primary)" @click="handleRefresh">
          <i mr-4 fs-14 i-mdi-refresh />
          刷新
        </el-button>
      </div>
    </div>
    <VirtualizedTable
      flex-1
      min-h-1
      :data="monitorInfos"
      :columns="columns"
      :row-actions="rowActions"
      @row-click="handleRowClick"
    ></VirtualizedTable>
  </div>
</template>

<style scoped></style>
