<script setup lang="ts">
import { ElInputNumber, ElSelect, ElOption } from 'element-plus';
import type { SellSetting } from '@/types';
import { BuyLevelsEnum } from '@/enum';

const props = defineProps<{
  modelValue: SellSetting['timed'];
  disabled?: boolean;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: SellSetting['timed']];
}>();

// 更新数据
const updateValue = (key: keyof SellSetting['timed'], value: any) => {
  const newValue = { ...props.modelValue };
  (newValue as any)[key] = value;
  emit('update:modelValue', newValue);
};

// 档位选项
const levelOptions = [
  { label: '买一', value: BuyLevelsEnum.买一 },
  { label: '买二', value: BuyLevelsEnum.买二 },
  { label: '买三', value: BuyLevelsEnum.买三 },
  { label: '买四', value: BuyLevelsEnum.买四 },
  { label: '买五', value: BuyLevelsEnum.买五 },
];
</script>

<template>
  <div flex="~ col" gap-4 :class="{ 'opacity-50 pointer-events-none': disabled }">
    <div grid="~ cols-2" gap-4>
      <div>
        <label text-xs text-gray-400>时间间隔(秒)</label>
        <ElInputNumber
          :model-value="modelValue.strategyDelayTime"
          :min="0"
          size="small"
          @update:model-value="(val) => updateValue('strategyDelayTime', val)"
        />
      </div>
      <div>
        <label text-xs text-gray-400>单笔数量(股)</label>
        <ElInputNumber
          :model-value="modelValue.strategyVolume"
          :min="0"
          size="small"
          @update:model-value="(val) => updateValue('strategyVolume', val)"
        />
      </div>
      <div>
        <label text-xs text-gray-400>未成撤单(秒)</label>
        <ElInputNumber
          :model-value="modelValue.cancelProtectedTime"
          :min="0"
          size="small"
          @update:model-value="(val) => updateValue('cancelProtectedTime', val)"
        />
      </div>
      <div>
        <label text-xs text-gray-400>总仓位(%)</label>
        <ElInputNumber
          :model-value="modelValue.positionPercent"
          :min="0"
          :max="100"
          size="small"
          @update:model-value="(val) => updateValue('positionPercent', val)"
        />
      </div>
      <div>
        <label text-xs text-gray-400>档位</label>
        <ElSelect
          :model-value="modelValue.priceFollowType"
          size="small"
          @update:model-value="(val) => updateValue('priceFollowType', val)"
        >
          <ElOption
            v-for="option in levelOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </ElSelect>
      </div>
    </div>
  </div>
</template>

<style scoped>
.el-input-number,
.el-select {
  width: 100%;
}
</style>
