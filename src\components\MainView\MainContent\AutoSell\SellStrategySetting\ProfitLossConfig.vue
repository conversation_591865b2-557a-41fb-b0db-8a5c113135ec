<script setup lang="ts">
import { ElInputNumber, ElCheckbox } from 'element-plus';
import type { SellSetting, ProfitLossSetting } from '@/types';

const props = defineProps<{
  modelValue: SellSetting['profitLoss'];
  disabled?: boolean;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: SellSetting['profitLoss']];
}>();

// 更新基础配置
const updateBasicValue = (key: keyof SellSetting['profitLoss'], value: any) => {
  const newValue = { ...props.modelValue };
  (newValue as any)[key] = value;
  emit('update:modelValue', newValue);
};

// 更新止盈止损配置
const updateProfitLossValue = (
  type: 'takeProfit' | 'StopLoss',
  index: number,
  key: keyof ProfitLossSetting,
  value: any
) => {
  const newValue = { ...props.modelValue };
  const newArray = [...newValue[type]];
  newArray[index] = { ...newArray[index], [key]: value };
  newValue[type] = newArray as any;
  emit('update:modelValue', newValue);
};
</script>

<template>
  <div flex="~ col" gap-4 :class="{ 'opacity-50 pointer-events-none': disabled }">
    <!-- 基础配置 -->
    <div>
      <div text-sm font-medium mb-2>基础配置</div>
      <div grid="~ cols-2" gap-4>
        <div>
          <ElCheckbox
            :model-value="modelValue.open"
            @update:model-value="(val) => updateBasicValue('open', val)"
          >
            启动撤单重单
          </ElCheckbox>
        </div>
        <div>
          <label text-xs text-gray-400>撤单间隔(毫秒)</label>
          <ElInputNumber
            :model-value="modelValue.time"
            :min="0"
            size="small"
            @update:model-value="(val) => updateBasicValue('time', val)"
          />
        </div>
        <div>
          <label text-xs text-gray-400>价格跌幅(%)</label>
          <ElInputNumber
            :model-value="modelValue.pricePercent"
            :min="0"
            :max="100"
            size="small"
            @update:model-value="(val) => updateBasicValue('pricePercent', val)"
          />
        </div>
      </div>
    </div>

    <!-- 止盈配置 -->
    <div>
      <div text-sm font-medium mb-2>止盈</div>
      <div v-for="(item, index) in modelValue.takeProfit" :key="`profit-${index}`" 
           flex="~ none" items-center gap-2 mb-2>
        <span text-xs w-8>{{ index + 1 }}</span>
        <div flex="~ 1" items-center gap-2>
          <ElCheckbox
            :model-value="item.open"
            @update:model-value="(val) => updateProfitLossValue('takeProfit', index, 'open', val)"
          />
          <label text-xs text-gray-400>幅度(%)</label>
          <ElInputNumber
            :model-value="item.amplitude"
            :min="0"
            :max="100"
            size="small"
            style="width: 80px"
            @update:model-value="(val) => updateProfitLossValue('takeProfit', index, 'amplitude', val)"
          />
          <label text-xs text-gray-400>仓位(%)</label>
          <ElInputNumber
            :model-value="item.positionPercent"
            :min="0"
            :max="100"
            size="small"
            style="width: 80px"
            @update:model-value="(val) => updateProfitLossValue('takeProfit', index, 'positionPercent', val)"
          />
        </div>
      </div>
    </div>

    <!-- 止损配置 -->
    <div>
      <div text-sm font-medium mb-2>止损</div>
      <div v-for="(item, index) in modelValue.StopLoss" :key="`loss-${index}`" 
           flex="~ none" items-center gap-2 mb-2>
        <span text-xs w-8>{{ index + 1 }}</span>
        <div flex="~ 1" items-center gap-2>
          <ElCheckbox
            :model-value="item.open"
            @update:model-value="(val) => updateProfitLossValue('StopLoss', index, 'open', val)"
          />
          <label text-xs text-gray-400>幅度(%)</label>
          <ElInputNumber
            :model-value="item.amplitude"
            :min="0"
            :max="100"
            size="small"
            style="width: 80px"
            @update:model-value="(val) => updateProfitLossValue('StopLoss', index, 'amplitude', val)"
          />
          <label text-xs text-gray-400>仓位(%)</label>
          <ElInputNumber
            :model-value="item.positionPercent"
            :min="0"
            :max="100"
            size="small"
            style="width: 80px"
            @update:model-value="(val) => updateProfitLossValue('StopLoss', index, 'positionPercent', val)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.el-input-number {
  width: 100%;
}
</style>
