import { ref } from 'vue';
import { defineStore } from 'pinia';
import type { Pool, PoolDetail } from '@/types';

// 股票选择状态管理
export const useStockSelectionStore = defineStore('stockSelection', () => {
  const selectedStock = ref<PoolDetail | null>(null);

  const setSelectedStock = (stock: PoolDetail | null) => {
    selectedStock.value = stock;
  };

  return {
    selectedStock,
    setSelectedStock,
  };
});

// 股票池选择状态管理
export const usePoolSelectionStore = defineStore('poolSelection', () => {
  const selectedPool = ref<Pool | null>(null);

  const setSelectedPool = (pool: Pool | null) => {
    selectedPool.value = pool;
  };

  return {
    selectedPool,
    setSelectedPool,
  };
});
