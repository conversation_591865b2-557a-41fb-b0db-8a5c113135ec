import type { BuyLevelsEnum, TacticStatusEnum } from '@/enum';

export interface PositionInfo {
  /** 持仓方向 */
  direction: number;
  /** 合约代码 */
  instrument: string;
  /** 合约名称 */
  instrumentName: string;
  /** 昨持仓 */
  yesterdayPosition: number;
  /** 今持仓 */
  todayPosition: number;
  /** 市值 */
  marketValue: number;
  /** 盈亏金额 */
  dayProfit: number;
  /** 盈亏比 */
  profitRatio: number;
  /** 仓位 */
  positionEffect: number;
  /** 执行进度 */
  progress: number;
  /** 运行状态 */
  status: TacticStatusEnum;
  /** 卖出策略 */
  setting: SellStrategy | null;
}

export interface TablePositionInfo extends PositionInfo {
  /** 涨跌幅，从行情获取后前端计算 */
  risePercent?: number;
}

export interface SellStrategy {
  /** 卖出策略ID */
  id: number;
  /** 卖出合约代码 */
  instrument: string;
  /** 卖出合约名称 */
  instrumentName: string;
  /** 策略详细配置 */
  setting: SellSetting;
}

export interface SellSetting {
  /** 启用低封单量 */
  openLower: boolean;
  /** 启用止盈止损 */
  openProfitLoss: boolean;
  /** 启用定时定量 */
  openTimed: boolean;
  /** 低封单量配置 */
  lowerVolume: {
    /** 优先 */
    priority: {
      /** 开始时间(13:49:43) */
      startTime: string;
      /** 结束时间(13:49:43) */
      endTime: string;
      /** 卖出仓位(%) */
      positionPercent: number;
      /** 封单金额少于(亿) */
      lineUpVolumeAmount: number;
      /** 封单金额少于(亿) */
      lineUpVolumeAmount1: number;
      /** xx(毫秒) */
      time: number;
      /** 封单下降(%) */
      downRate: number;
    };
    /** 全局 */
    global: {
      /** 开始时间(13:49:43) */
      startTime: string;
      /** 结束时间(13:49:43) */
      endTime: string;
      /** 卖出仓位(%) */
      positionPercent: number;
      /** 封单金额少于(亿) */
      lineUpVolumeAmount: number;
      /** 封单金额少于(亿) */
      lineUpVolumeAmount1: number;
      /** xx(毫秒) */
      time: number;
      /** 封单下降(%) */
      downRate: number;
    };
  };
  /** 止盈止损配置 */
  profitLoss: {
    /** 启动撤单重单 */
    open: boolean;
    /** 撤单间隔(毫秒) */
    time: number;
    /** 价格跌幅(%) */
    pricePercent: number;
    /** 止盈 */
    takeProfit: [
      ProfitLossSetting,
      ProfitLossSetting,
      ProfitLossSetting,
      ProfitLossSetting,
      ProfitLossSetting,
    ];
    /** 止损 */
    StopLoss: [
      ProfitLossSetting,
      ProfitLossSetting,
      ProfitLossSetting,
      ProfitLossSetting,
      ProfitLossSetting,
    ];
  };
  /** 定时定量配置 */
  timed: {
    /** 时间间隔(秒) */
    strategyDelayTime: number;
    /** 单笔数量(股) */
    strategyVolume: number;
    /** 未成撤单(秒) */
    cancelProtectedTime: number;
    /** 总仓位(%) */
    positionPercent: number;
    /** 档位(买一~买五) */
    priceFollowType: BuyLevelsEnum;
  };
}

export interface ProfitLossSetting {
  /** 幅度(%) */
  amplitude: number;
  /** 仓位(%) */
  positionPercent: number;
  /** 是否启用 */
  open: boolean;
}
