<script setup lang="ts">
import { ref, shallowRef, computed, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { usePoolSelectionStore } from '@/stores';
import { PoolService } from '@/api';
import { ElMessage } from 'element-plus';
import { isStopped } from '@/enum';
import type {
  Pool,
  StrategyConfig,
  CancelConfig1,
  CancelConfig2,
  CancelConfig3,
  EffectTime,
} from '@/types';
import { Utils } from '@/script';

const defaultStrategyConfig: StrategyConfig = {
  limitTriggerNum: null,
  shConfig: [
    { firstAmount: null, secondAmount: null, positionEffect: null },
    { firstAmount: null, secondAmount: null, positionEffect: null },
    { firstAmount: null, secondAmount: null, positionEffect: null },
  ],
  szConfig: [
    { firstAmount: null, secondAmount: null, positionEffect: null },
    { firstAmount: null, secondAmount: null, positionEffect: null },
    { firstAmount: null, secondAmount: null, positionEffect: null },
  ],
};

const defaultCancelConfig1: CancelConfig1 = {
  cancelType: 1,
  cancelInfo: { tradeTime: null },
};

const defaultCancelConfig2: CancelConfig2 = {
  cancelType: 2,
  cancelInfo: { orderTime: null, frontAmount: null, afterAmount: null },
};

const defaultCancelConfig3: CancelConfig3 = {
  cancelType: 3,
  cancelInfo: { volume: null, time: null, downRate: null },
};

const defaultEffectTime: EffectTime = {
  am: { begin: '09:30:00', end: '11:30:00' },
  pm: { begin: '13:00:00', end: '14:57:00' },
};

const { selectedPool } = storeToRefs(usePoolSelectionStore());

// 当前激活的tab
const activeTab = ref('buyTrigger');

// 原始配置数据（用于取消时还原）
const originalConfig = shallowRef<Pool | null>(null);

// 表单数据
const formData = ref({
  // 买入触发配置
  strategyConfig: Utils.deepClone(defaultStrategyConfig),
  // 撤单配置 - 使用具体的类型结构
  cancelConfig: {
    config1: Utils.deepClone(defaultCancelConfig1),
    config2: Utils.deepClone(defaultCancelConfig2),
    config3: Utils.deepClone(defaultCancelConfig3),
  },
  // 运行时间配置
  effectTime: Utils.deepClone(defaultEffectTime),
});

// 撤单条件启用状态
const cancelEnabled = ref({
  config1: false,
  config2: false,
  config3: false,
});

// 是否禁用表单（运行中时禁用）
const isDisabled = computed(() => {
  return !isStopped(selectedPool.value);
});

// 监听选中股票池变化
watch(
  selectedPool,
  newPool => {
    if (newPool) {
      // 保存原始配置
      originalConfig.value = Utils.deepClone(newPool);
      // 更新表单数据\
      if (newPool.strategyConfig) {
        formData.value.strategyConfig = { ...newPool.strategyConfig };
      } else {
        formData.value.strategyConfig = Utils.deepClone(defaultStrategyConfig);
      }
      // 转换撤单配置数组为对象结构
      const cancelConfigs = newPool.cancelConfig;
      if (cancelConfigs) {
        const config1 = cancelConfigs.find(c => c.cancelType === 1) as CancelConfig1;
        const config2 = cancelConfigs.find(c => c.cancelType === 2) as CancelConfig2;
        const config3 = cancelConfigs.find(c => c.cancelType === 3) as CancelConfig3;

        formData.value.cancelConfig.config1 = config1 || formData.value.cancelConfig.config1;
        formData.value.cancelConfig.config2 = config2 || formData.value.cancelConfig.config2;
        formData.value.cancelConfig.config3 = config3 || formData.value.cancelConfig.config3;

        // 设置撤单条件启用状态
        cancelEnabled.value.config1 = !!config1;
        cancelEnabled.value.config2 = !!config2;
        cancelEnabled.value.config3 = !!config3;
      } else {
        formData.value.cancelConfig = {
          config1: Utils.deepClone(defaultCancelConfig1),
          config2: Utils.deepClone(defaultCancelConfig2),
          config3: Utils.deepClone(defaultCancelConfig3),
        };
        // 重置撤单条件启用状态
        cancelEnabled.value.config1 = false;
        cancelEnabled.value.config2 = false;
        cancelEnabled.value.config3 = false;
      }
      if (newPool.effectTime) {
        formData.value.effectTime = { ...newPool.effectTime };
      } else {
        formData.value.effectTime = Utils.deepClone(defaultEffectTime);
      }
    } else {
      originalConfig.value = null;
    }
  },
  { immediate: true },
);

// 保存配置
const handleSave = async () => {
  if (!selectedPool.value) return;

  try {
    // 将对象结构转换为数组结构，只包含启用的撤单条件
    const cancelConfigArray = [];
    if (cancelEnabled.value.config1) {
      cancelConfigArray.push(formData.value.cancelConfig.config1);
    }
    if (cancelEnabled.value.config2) {
      cancelConfigArray.push(formData.value.cancelConfig.config2);
    }
    if (cancelEnabled.value.config3) {
      cancelConfigArray.push(formData.value.cancelConfig.config3);
    }

    const { errorCode, errorMsg } = await PoolService.updateStrategyPool({
      ...selectedPool.value,
      strategyConfig: formData.value.strategyConfig,
      cancelConfig: cancelConfigArray,
      effectTime: formData.value.effectTime,
    });

    if (errorCode === 0) {
      ElMessage.success('配置保存成功');
    } else {
      ElMessage.error(errorMsg || '保存失败');
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('保存失败');
  }
};

// 取消配置（还原为服务端配置）
const handleCancel = () => {
  if (originalConfig.value) {
    formData.value.strategyConfig = Utils.deepClone(
      originalConfig.value.strategyConfig || defaultStrategyConfig,
    );
    // 转换撤单配置数组为对象结构
    const cancelConfigs = originalConfig.value.cancelConfig || [];
    const config1 = cancelConfigs.find(c => c.cancelType === 1) as CancelConfig1;
    const config2 = cancelConfigs.find(c => c.cancelType === 2) as CancelConfig2;
    const config3 = cancelConfigs.find(c => c.cancelType === 3) as CancelConfig3;

    formData.value.cancelConfig.config1 = config1 || Utils.deepClone(defaultCancelConfig1);
    formData.value.cancelConfig.config2 = config2 || Utils.deepClone(defaultCancelConfig2);
    formData.value.cancelConfig.config3 = config3 || Utils.deepClone(defaultCancelConfig3);

    // 还原撤单条件启用状态
    cancelEnabled.value.config1 = !!config1;
    cancelEnabled.value.config2 = !!config2;
    cancelEnabled.value.config3 = !!config3;

    formData.value.effectTime = Utils.deepClone(
      originalConfig.value.effectTime || defaultEffectTime,
    );
    ElMessage.info('已还原为服务端配置');
  }
};
</script>

<template>
  <div flex="~ col" h-full>
    <div h-32 flex aic px-16 jcsb>
      <div font-bold>股票池配置</div>
      <div v-if="selectedPool">
        <el-button :disabled="isDisabled" @click="handleCancel">取消</el-button>
        <el-button :disabled="isDisabled" color="var(--g-primary)" @click="handleSave">
          保存
        </el-button>
      </div>
    </div>

    <div v-if="!selectedPool" flex-1 flex aic jcc text-gray-400>请选择股票池</div>

    <div v-else flex="~ col" flex-1 min-h-1 pb-16>
      <el-tabs mb-10 type="card" v-model="activeTab">
        <el-tab-pane label="买入触发" name="buyTrigger"></el-tab-pane>
        <el-tab-pane label="撤单" name="cancel"></el-tab-pane>
        <el-tab-pane label="运行时间" name="runtime"></el-tab-pane>
      </el-tabs>
      <div v-if="activeTab === 'buyTrigger'" class="config-content">
        <div px-16 mb-16>
          <div flex aic gap-8>
            <span>涨停排名前</span>
            <el-input-number
              v-model="formData.strategyConfig.limitTriggerNum"
              :disabled="isDisabled"
              :min="1"
              :max="100"
              :step="1"
              style="width: 100px"
            />
            <span>只</span>
          </div>
        </div>

        <!-- 沪市配置 -->
        <div px-16 mt-10>
          <div font-bold mb-12 fs-14>沪市</div>
          <el-table :data="formData.strategyConfig.shConfig">
            <el-table-column label="" width="80" align="center">
              <template #default="{ $index }">
                <span>委托{{ $index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="首次排单金额(万)" align="center">
              <template #default="{ row }">
                <el-input-number
                  v-model="row.firstAmount"
                  :disabled="isDisabled"
                  :min="0"
                  :step="100"
                  size="small"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column label="二次排单金额(万)" align="center">
              <template #default="{ row }">
                <el-input-number
                  v-model="row.secondAmount"
                  :disabled="isDisabled"
                  :min="0"
                  :step="100"
                  size="small"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column label="仓位比例" align="center">
              <template #default="{ row }">
                <div flex aic>
                  <el-input-number
                    v-model="row.positionEffect"
                    :disabled="isDisabled"
                    :min="0"
                    :max="100"
                    :step="1"
                    size="small"
                    style="width: 100%"
                  >
                    <template #suffix>%</template>
                  </el-input-number>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 深市配置 -->
        <div px-16 mt-10>
          <div font-bold mb-12 fs-14>深市</div>
          <el-table :data="formData.strategyConfig.szConfig">
            <el-table-column label="" width="80" align="center">
              <template #default="{ $index }">
                <span>委托{{ $index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="首次排单金额(万)" align="center">
              <template #default="{ row }">
                <el-input-number
                  v-model="row.firstAmount"
                  :disabled="isDisabled"
                  :min="0"
                  :step="100"
                  size="small"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column label="二次排单金额(万)" align="center">
              <template #default="{ row }">
                <el-input-number
                  v-model="row.secondAmount"
                  :disabled="isDisabled"
                  :min="0"
                  :step="100"
                  size="small"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column label="仓位比例" align="center">
              <template #default="{ row }">
                <div flex aic>
                  <el-input-number
                    v-model="row.positionEffect"
                    :disabled="isDisabled"
                    :min="0"
                    :max="100"
                    :step="1"
                    size="small"
                    style="width: 100%"
                  >
                    <template #suffix>%</template>
                  </el-input-number>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div px-16 v-if="activeTab === 'cancel'" class="config-content">
        <!-- 撤单类型1：当一笔成交时间小于N秒，则撤单 -->
        <div class="cancel-config-item" mb-24>
          <div flex aic gap-8 mb-12>
            <el-checkbox v-model="cancelEnabled.config1" :disabled="isDisabled" />
            <div font-bold>撤单1</div>
          </div>
          <div flex aic gap-8 ml-24>
            <span>当第一笔成交时间小于</span>
            <el-input-number
              v-model="formData.cancelConfig.config1.cancelInfo.tradeTime"
              :disabled="isDisabled || !cancelEnabled.config1"
              :min="1"
              :step="10"
              style="width: 100px"
            />
            <span>毫秒，则撤单</span>
          </div>
        </div>

        <!-- 撤单类型2：下单时间、前少、后多 -->
        <div class="cancel-config-item" mb-24>
          <div flex aic gap-8 mb-12>
            <el-checkbox v-model="cancelEnabled.config2" :disabled="isDisabled" />
            <div font-bold>撤单2</div>
          </div>
          <div flex flex-wrap aic gap-8 ml-24>
            <span>下单</span>
            <el-input-number
              v-model="formData.cancelConfig.config2.cancelInfo.orderTime"
              :disabled="isDisabled || !cancelEnabled.config2"
              :min="1"
              :step="10"
              style="width: 100px"
            />
            <span>毫秒之后，前面少于</span>
            <el-input-number
              v-model="formData.cancelConfig.config2.cancelInfo.frontAmount"
              :disabled="isDisabled || !cancelEnabled.config2"
              :min="0"
              :step="100"
              style="width: 100px"
            />
            <span>万元时，若后面多于</span>
            <el-input-number
              v-model="formData.cancelConfig.config2.cancelInfo.afterAmount"
              :disabled="isDisabled || !cancelEnabled.config2"
              :min="0"
              :step="100"
              style="width: 100px"
            />
            <span>万元时，则撤单</span>
          </div>
        </div>

        <!-- 撤单类型3：封单手数、时间、回落比例 -->
        <div class="cancel-config-item" mb-24>
          <div flex aic gap-8 mb-12>
            <el-checkbox v-model="cancelEnabled.config3" :disabled="isDisabled" />
            <div font-bold>撤单3</div>
          </div>
          <div flex flex-wrap aic gap-8 ml-24>
            <span>封单手数低于</span>
            <el-input-number
              v-model="formData.cancelConfig.config3.cancelInfo.volume"
              :disabled="isDisabled || !cancelEnabled.config3"
              :min="0"
              :step="1000"
              style="width: 100px"
            />
            <span>手</span>
            <el-input-number
              v-model="formData.cancelConfig.config3.cancelInfo.time"
              :disabled="isDisabled || !cancelEnabled.config3"
              :min="1"
              :step="10"
              style="width: 100px"
            />
            <span>毫秒内，回落比例下降超过</span>
            <el-input-number
              v-model="formData.cancelConfig.config3.cancelInfo.downRate"
              :disabled="isDisabled || !cancelEnabled.config3"
              :min="0"
              :max="100"
              :step="1"
              style="width: 100px"
            >
              <template #suffix>%</template>
            </el-input-number>
            <span>，则撤单</span>
          </div>
        </div>
      </div>
      <div px-16 v-if="activeTab === 'runtime'" class="config-content">
        <!-- 股票池监控开始时间1 -->
        <div flex aic gap-8 mb-16>
          <span>股票池监控开始时间1</span>
          <el-time-picker
            v-model="formData.effectTime.am.begin"
            :disabled="isDisabled"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            placeholder="选择时间"
            style="width: 200px"
          />
        </div>

        <!-- 股票池监控结束时间1 -->
        <div flex aic gap-8 mb-16>
          <span>股票池监控结束时间1</span>
          <el-time-picker
            v-model="formData.effectTime.am.end"
            :disabled="isDisabled"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            placeholder="选择时间"
            style="width: 200px"
          />
        </div>

        <!-- 股票池监控开始时间2 -->
        <div flex aic gap-8 mb-16>
          <span>股票池监控开始时间2</span>
          <el-time-picker
            v-model="formData.effectTime.pm.begin"
            :disabled="isDisabled"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            placeholder="选择时间"
            style="width: 200px"
          />
        </div>

        <!-- 股票池监控结束时间2 -->
        <div flex aic gap-8 mb-16>
          <span>股票池监控结束时间2</span>
          <el-time-picker
            v-model="formData.effectTime.pm.end"
            :disabled="isDisabled"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            placeholder="选择时间"
            style="width: 200px"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
